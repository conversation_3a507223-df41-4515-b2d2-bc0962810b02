from sqlalchemy import (
    Column,
    Integer,
    String,
    Table,
    ForeignKey,
    Index,
    PrimaryKeyConstraint,
    Enum as SQLAEnum,
)
from sqlalchemy.orm import relationship
from src.db.base import Base
from src.db.models.mixins import TenantMixin
from src.db.enums import RoleType

# Tabla de asociación para roles y permisos
role_permissions = Table(
    "role_permissions",
    Base.metadata,
    Column(
        "role_id", Integer, ForeignKey("roles.id", ondelete="CASCADE"), primary_key=True
    ),
    Column(
        "permission_id",
        Integer,
        ForeignKey("permissions.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    <PERSON>umn(
        "account_id",
        Integer,
        ForeignKey("accounts.account_id", ondelete="CASCADE"),
        primary_key=True,
    ),
)


class Role(Base, TenantMixin):
    """Modelo para roles con soporte multi-tenant"""

    __tablename__ = "roles"

    # La otra parte de la PK compuesta
    id = Column(Integer, nullable=False)

    # Columnas adicionales
    name = Column(SQLAEnum(RoleType), nullable=False)
    description = Column(String(255))

    # Relaciones
    permissions = relationship(
        "Permission", secondary=role_permissions, back_populates="roles"
    )
    system_user_roles = relationship(
        "SystemUserRole",
        back_populates="role",
        foreign_keys="[SystemUserRole.role_id]",
        primaryjoin="and_(Role.account_id==SystemUserRole.account_id, Role.id==SystemUserRole.role_id)",
        passive_deletes=True,
    )
    users = relationship(
        "SystemUser",
        secondary="system_user_roles",
        back_populates="roles",
        viewonly=True,  # Avoids SQLAlchemy managing the association table from this side.
                       # Use SystemUserRole relationship to manage the association.
    )

    # Definición explícita de la PK compuesta e índices
    __table_args__ = (
        PrimaryKeyConstraint("account_id", "id"),
        Index("idx_role_name", "name"),
        Index("idx_role_account", "account_id"),
    ) + getattr(TenantMixin, "__table_args__", ())
