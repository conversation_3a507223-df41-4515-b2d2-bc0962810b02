from fastapi import Request, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from sqlalchemy.exc import SQLAlchemyError
from src.core.exceptions import ErrorCode, CustomHTTPException
from src.utils.base_logger import logger
import traceback


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        try:
            logger.info(f"Processing request: {request.method} {request.url.path}")
            response = await call_next(request)
            return response
        except Exception as exc:
            error_id = None
            error_stack = "".join(
                traceback.format_exception(type(exc), exc, exc.__traceback__)
            )

            # Manejar excepciones personalizadas y estándar
            if isinstance(exc, CustomHTTPException):
                # Ya tiene el formato correcto con error_code
                status_code = exc.status_code
                detail = exc.detail
            elif isinstance(exc, SQLAlchemyError):
                status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
                detail = {
                    "message": "Database error occurred",
                    "error_code": ErrorCode.DATABASE_ERROR
                }
                logger.error(f"Database error: {str(exc)}\nStack trace:\n{error_stack}")
            else:
                # Cualquier otra excepción no manejada
                status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
                detail = {
                    "message": "Internal server error",
                    "error_code": ErrorCode.INTERNAL_ERROR
                }
                logger.error(
                    f"Unhandled error: {str(exc)}\nStack trace:\n{error_stack}"
                )

            # Construir respuesta con formato estándar
            response_content = {
                "path": request.url.path,
            }

            # Si detail ya es un diccionario, fusionarlo con response_content
            if isinstance(detail, dict):
                response_content.update(detail)
            else:
                # Si es una cadena, convertirla al formato estándar
                response_content["message"] = detail
                response_content["error_code"] = ErrorCode.INTERNAL_ERROR

            # Añadir error_id si existe
            if error_id:
                response_content["error_id"] = error_id

            return JSONResponse(
                status_code=status_code,
                content=response_content
            )
