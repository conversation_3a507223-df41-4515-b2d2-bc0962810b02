"""
Modelo para trabajos de ingesta masiva de datos.
"""

from sqlalchemy import Column, Integer, String, DateTime, JSON, ForeignKey, Text, Enum, Index, PrimaryKeyConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from src.db.base import Base
from src.db.enums import BatchIngestionJobStatus
from .mixins import TenantMixin, ACCOUNT_ID_FK, ACCOUNT_RANGE


class BatchIngestionJob(Base, TenantMixin):
    """
    Modelo para trabajos de ingesta masiva de datos.
    """
    __tablename__ = "batch_ingestion_jobs"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    id = Column(Integer, primary_key=True, index=True)
    status = Column(Enum(BatchIngestionJobStatus), index=True, default=BatchIngestionJobStatus.PENDING)
    created_at = Column(DateTime, default=func.now())
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)
    task_id = Column(String, nullable=True)  # ID de la tarea Celery
    processed_count = Column(JSON, nullable=True)  # Contador de elementos procesados
    parameters = Column(JSON, nullable=True)  # Parámetros de la tarea
    data_file_path = Column(String, nullable=True)  # Ruta al archivo de datos en GCS o sistema de archivos local

    __table_args__ = (
        PrimaryKeyConstraint("account_id", "id"),
        Index("idx_batch_ingestion_job_status", "account_id", "status"),
        Index("idx_batch_ingestion_job_created_at", "account_id", "created_at"),
        {"postgresql_partition_by": ACCOUNT_RANGE},
    )

    # Relationships
    account = relationship("Account", back_populates="batch_ingestion_jobs")
