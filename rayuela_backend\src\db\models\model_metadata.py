from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    ForeignKey,
    Text,
    UniqueConstraint,
    Index,
    func,
    PrimaryKeyConstraint,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from src.db.base import Base
from .mixins import TenantMixin, ACCOUNT_ID_FK, ACCOUNT_RANGE


class ModelMetadata(Base, TenantMixin):
    __tablename__ = "artifact_metadata"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    id = Column(Integer, primary_key=True)
    artifact_name = Column(String, index=True)
    artifact_version = Column(String)
    description = Column(Text, nullable=True)
    training_date = Column(DateTime, default=func.now())
    performance_metrics = Column(JSONB)
    parameters = Column(JSONB)
    artifacts_path = Column(String, nullable=False)

    __table_args__ = (
        PrimaryKeyConstraint("account_id", "id"),
        UniqueConstraint(
            "account_id",
            "artifact_name",
            "artifact_version",
            name="uq_artifact_name_version",
        ),
        Index(
            "idx_artifact_account_name_version",
            "account_id",
            "artifact_name",
            "artifact_version",
        ),
        Index("idx_artifact_account_date", "account_id", "training_date"),
        {"postgresql_partition_by": ACCOUNT_RANGE},
    )

    # Relationships
    account = relationship("Account", back_populates="artifact_metadata")
    training_jobs = relationship(
        "TrainingJob",
        back_populates="artifact_metadata",
        viewonly=True  # Avoids SQLAlchemy managing the relationship from this side.
                      # The relationship is managed through the TrainingJob model.
    )
    metrics = relationship(
        "ModelMetric",
        back_populates="model_metadata",
        cascade="all, delete-orphan",
        foreign_keys="ModelMetric.model_metadata_id"
    )
    model_metrics = relationship(
        "ModelMetric", 
        back_populates="model", 
        cascade="all, delete-orphan",
        foreign_keys="ModelMetric.model_metadata_id",
        overlaps="metrics"
    )
    training_metrics = relationship("TrainingMetrics", back_populates="model", cascade="all, delete-orphan")
