# Gestión de Particiones para Tablas de Alto Volumen

Este documento describe el sistema de particionamiento utilizado en Rayuela para manejar tablas de alto volumen como `interactions` y `audit_logs`, así como las herramientas y procedimientos para su mantenimiento.

## Descripción General

Rayuela utiliza particionamiento por rango de `account_id` para dividir las tablas de alto volumen en fragmentos más pequeños y manejables. Esto mejora significativamente:

- El rendimiento de consultas que filtran por `account_id`
- La eficiencia de operaciones de mantenimiento (como eliminación de datos antiguos)
- La capacidad de escalar horizontalmente

## Tablas Particionadas

Las siguientes tablas están configuradas para usar particionamiento por rango:

```
ALLOWED_TABLES = {
    "accounts",
    "subscriptions",
    "system_users",
    "roles",
    "permissions",
    "role_permissions",
    "system_user_roles",
    "audit_logs",
    "end_users",
    "products",
    "interactions",
    "searches",
    "artifact_metadata",
    "training_jobs",
    "account_usage_metrics",
}
```

## Script de Gestión de Particiones

El script `scripts/manage_partitions.py` es una herramienta crítica que:

1. Verifica las particiones existentes para cada tabla
2. Calcula el rango actual basado en el `account_id` máximo
3. Crea proactivamente nuevas particiones para acomodar el crecimiento futuro

### Funcionamiento

El script:

- Consulta el `account_id` máximo actual en la base de datos
- Para cada tabla particionada, verifica si existen particiones para el rango actual
- Crea particiones adicionales según el parámetro `buffer_count` (por defecto 5)
- Utiliza el tamaño de partición definido en `settings.PARTITION_SIZE` (por defecto 100,000)

### Ejecución Manual

Para ejecutar el script manualmente:

```bash
# Desde la raíz del proyecto
python scripts/manage_partitions.py
```

### Configuración

El comportamiento del script se controla mediante las siguientes variables de entorno o configuraciones:

| Variable | Descripción | Valor por defecto |
|----------|-------------|-------------------|
| `PARTITION_SIZE` | Tamaño de cada partición (rango de account_id) | 100,000 |
| `PARTITION_BUFFER_COUNT` | Número de particiones adicionales a crear | 5 |

## Integración con CI/CD y Programación

### Opción 1: Kubernetes CronJob

Para entornos Kubernetes, se recomienda configurar un CronJob:

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: partition-management
spec:
  schedule: "0 1 * * *"  # Ejecutar diariamente a la 1:00 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: partition-manager
            image: rayuela-backend:latest
            command: ["python", "scripts/manage_partitions.py"]
            env:
              - name: POSTGRES_USER
                valueFrom:
                  secretKeyRef:
                    name: db-credentials
                    key: username
              - name: POSTGRES_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: db-credentials
                    key: password
              # Otras variables de entorno necesarias
          restartPolicy: OnFailure
```

### Opción 2: Google Cloud Scheduler con Cloud Run

Para entornos Google Cloud:

1. Crear un servicio Cloud Run que ejecute el script:

```bash
gcloud run deploy partition-manager \
  --image gcr.io/[PROJECT_ID]/rayuela-backend:latest \
  --command "python" \
  --args "scripts/manage_partitions.py" \
  --no-allow-unauthenticated
```

2. Configurar Cloud Scheduler para invocar el servicio:

```bash
gcloud scheduler jobs create http partition-management-job \
  --schedule="0 1 * * *" \
  --uri="https://partition-manager-[HASH].run.app/" \
  --http-method=GET \
  --oidc-service-account-email=[SERVICE_ACCOUNT]@[PROJECT_ID].iam.gserviceaccount.com
```

### Opción 3: Celery Beat

Para integrar con el sistema de tareas programadas de Celery:

1. Crear una tarea Celery en `src/workers/celery_tasks.py`:

```python
@celery_app.task(name="manage_partitions")
def manage_partitions_task():
    """Celery task to manage database partitions."""
    from scripts.manage_partitions import manage_partitions
    import asyncio
    
    asyncio.run(manage_partitions())
```

2. Configurar la programación en `src/workers/celery_app.py`:

```python
celery_app.conf.beat_schedule.update({
    "manage-partitions": {
        "task": "src.workers.celery_tasks.manage_partitions_task",
        "schedule": 86400.0,  # Una vez al día
    },
})
```

## Monitoreo y Mantenimiento

### Verificación de Particiones

Para verificar las particiones existentes en PostgreSQL:

```sql
SELECT
    parent.relname AS parent_table,
    child.relname AS partition,
    pg_size_pretty(pg_relation_size(child.oid)) AS partition_size
FROM pg_inherits
JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
JOIN pg_class child ON pg_inherits.inhrelid = child.oid
JOIN pg_namespace nmsp_parent ON nmsp_parent.oid = parent.relnamespace
JOIN pg_namespace nmsp_child ON nmsp_child.oid = child.relnamespace
WHERE parent.relname IN ('interactions', 'audit_logs')
ORDER BY parent.relname, child.relname;
```

### Alertas y Monitoreo

Se recomienda configurar alertas para:

1. Fallos en la ejecución del script de gestión de particiones
2. Crecimiento anómalo de tablas específicas
3. Particiones que se acercan a su capacidad máxima

## Mejores Prácticas

1. **Ejecución Regular**: Ejecutar el script de gestión de particiones diariamente para asegurar que siempre haya particiones disponibles.

2. **Monitoreo**: Verificar regularmente el tamaño de las particiones y ajustar `PARTITION_SIZE` si es necesario.

3. **Respaldo**: Incluir la verificación de particiones como parte del proceso de respaldo y recuperación.

4. **Pruebas**: Probar el script en entornos de desarrollo/staging antes de aplicarlo en producción.

5. **Documentación**: Mantener actualizada esta documentación con cualquier cambio en la estrategia de particionamiento.

## Solución de Problemas

### Particiones Faltantes

Si se detectan errores relacionados con particiones faltantes:

1. Verificar los logs del script de gestión de particiones
2. Ejecutar manualmente el script con logging detallado:
   ```bash
   PYTHONPATH=. python scripts/manage_partitions.py
   ```
3. Verificar que las credenciales de base de datos tengan permisos suficientes

### Rendimiento Degradado

Si se observa degradación del rendimiento:

1. Verificar que las consultas estén utilizando las particiones correctamente (usando EXPLAIN)
2. Revisar si el tamaño de partición es adecuado para el volumen de datos
3. Considerar ajustar índices para mejorar el rendimiento de consultas específicas
