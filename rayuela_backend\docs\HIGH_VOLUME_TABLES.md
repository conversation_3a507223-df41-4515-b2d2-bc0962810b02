# Gestión de Tablas de Alto Volumen

Este documento describe las estrategias implementadas para gestionar el crecimiento de tablas de alto volumen en la aplicación, específicamente las tablas `interactions` y `audit_logs`.

## Desafíos

Las tablas `interactions` y `audit_logs` pueden crecer muy rápidamente debido a su naturaleza:

1. **Interacciones**: Cada interacción de usuario con un producto se registra en esta tabla. En un sistema de recomendación activo, esto puede significar millones de registros en poco tiempo.

2. **Logs de Auditoría**: Cada acción administrativa y cambio importante en el sistema se registra en esta tabla, lo que puede resultar en un gran volumen de datos a lo largo del tiempo.

Aunque el particionamiento por `account_id` ayuda a mejorar el rendimiento de las consultas, estas tablas seguirán creciendo indefinidamente si no se implementan estrategias de gestión adecuadas.

## Estrategias Implementadas

### 1. Limpieza Periódica

Se han implementado funciones de limpieza que eliminan registros antiguos basados en políticas de retención configurables:

- **Logs de Auditoría**: Por defecto, se mantienen durante 90 días.
- **Interacciones**: Por defecto, se mantienen durante 180 días.

Estas funciones se ejecutan periódicamente como tareas programadas de Celery:

```python
# Configuración en celery_app.py
celery_app.conf.beat_schedule = {
    "cleanup-old-audit-logs": {
        "task": "src.workers.celery_tasks.cleanup_old_audit_logs",
        "schedule": 86400.0,  # Una vez al día
        "kwargs": {"days_to_keep": 90},
    },
    "cleanup-old-interactions": {
        "task": "src.workers.celery_tasks.cleanup_old_interactions",
        "schedule": 86400.0 * 7,  # Una vez a la semana
        "kwargs": {"days_to_keep": 180, "batch_size": 10000},
    },
}
```

### 2. Eliminación por Lotes

Para evitar bloqueos prolongados durante la eliminación de grandes cantidades de datos, la función `cleanup_old_interactions` utiliza una estrategia de eliminación por lotes:

```python
# Eliminar en lotes para evitar bloqueos prolongados
total_deleted = 0
while True:
    # Identificar IDs a eliminar en este lote
    batch_query = text(
        f"""
        WITH ids_to_delete AS (
            SELECT account_id, id
            FROM interactions
            WHERE timestamp < :cutoff_date {account_condition}
            ORDER BY timestamp ASC
            LIMIT :batch_size
        )
        DELETE FROM interactions i
        USING ids_to_delete d
        WHERE i.account_id = d.account_id AND i.id = d.id
        RETURNING i.account_id, i.id
        """
    )
    
    batch_params = {**params, "batch_size": batch_size}
    result = await db.execute(batch_query, batch_params)
    deleted_rows = result.fetchall()
    deleted_count = len(deleted_rows)
    
    if deleted_count == 0:
        break
        
    total_deleted += deleted_count
    await db.commit()
    
    # Si eliminamos menos que el tamaño del lote, hemos terminado
    if deleted_count < batch_size:
        break
```

### 3. Particionamiento

Ambas tablas utilizan particionamiento por rango de `account_id` para mejorar el rendimiento:

```python
__table_args__ = (
    PrimaryKeyConstraint("account_id", "id"),
    Index("idx_interaction_account_timestamp", "account_id", "timestamp"),
    {"postgresql_partition_by": ACCOUNT_RANGE},
)
```

Esto permite que las operaciones de limpieza sean más eficientes, ya que pueden enfocarse en particiones específicas.

### 4. Índices Optimizados

Se han creado índices específicos para mejorar el rendimiento de las consultas más comunes:

**Interacciones**:
```python
Index("idx_interaction_account_user", "account_id", "end_user_id"),
Index("idx_interaction_account_product", "account_id", "product_id"),
Index("idx_interaction_account_timestamp", "account_id", "timestamp"),
```

**Logs de Auditoría**:
```python
Index("idx_audit_account_timestamp", "account_id", "created_at"),
```

### 5. Monitorización

Se ha implementado una tarea periódica para monitorear el tamaño y crecimiento de estas tablas:

```python
@celery_app.task(name="monitor_high_volume_tables")
def monitor_high_volume_tables() -> Dict[str, Any]:
    """
    Celery task to monitor high volume tables.
    """
    # Implementación...
```

Esta tarea recopila estadísticas sobre el tamaño de las tablas, la distribución de datos por cuenta y por fecha, lo que permite identificar patrones de crecimiento y posibles problemas.

## API de Mantenimiento

Se ha creado una API de mantenimiento que permite a los administradores ejecutar tareas de limpieza y monitoreo bajo demanda:

```
POST /api/v1/maintenance/cleanup-audit-logs
POST /api/v1/maintenance/cleanup-interactions
POST /api/v1/maintenance/monitor-tables
GET /api/v1/maintenance/task/{task_id}
```

Estas operaciones pueden ejecutarse de forma sincrónica o asincrónica, y proporcionan información detallada sobre los resultados.

## Consideraciones Futuras

### 1. Archivado

Para datos que deben conservarse por razones legales o de análisis histórico, pero que no necesitan estar en las tablas principales, se podría implementar un sistema de archivado:

```python
async def archive_old_data(
    table_name: str,
    days_to_archive: int,
    archive_schema: str = "archive",
    account_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    Archiva datos antiguos a un esquema de archivo.
    """
    # Implementación...
```

Este enfoque movería los datos antiguos a tablas separadas en un esquema de archivo, manteniendo las tablas principales más pequeñas y eficientes.

### 2. Particionamiento por Tiempo

Además del particionamiento por `account_id`, se podría implementar particionamiento por tiempo para facilitar aún más la gestión de datos históricos:

```python
async def create_time_partitions(
    table_name: str, 
    interval: str = "month", 
    future_partitions: int = 3
) -> Dict[str, Any]:
    """
    Crea particiones por tiempo para una tabla.
    """
    # Implementación...
```

### 3. Compresión de Datos

Para datos históricos que se consultan con poca frecuencia, se podría implementar compresión a nivel de tabla o partición en PostgreSQL.

## Conclusión

La gestión efectiva de tablas de alto volumen requiere un enfoque multifacético que incluye limpieza periódica, particionamiento, indexación optimizada y monitoreo. Las estrategias implementadas proporcionan una base sólida para manejar el crecimiento de datos, pero deben revisarse y ajustarse regularmente a medida que cambian los patrones de uso y el volumen de datos.
