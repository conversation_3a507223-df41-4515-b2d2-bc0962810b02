apiVersion: batch/v1
kind: CronJob
metadata:
  name: manage-partitions
  namespace: rayuela
spec:
  # Ejecutar cada día a las 2:00 AM
  schedule: "0 2 * * *"
  # Política de concurrencia: no permitir ejecuciones concurrentes
  concurrencyPolicy: Forbid
  # Historial de trabajos completados a mantener
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 5
  jobTemplate:
    spec:
      # Tiempo máximo de ejecución antes de considerar el trabajo como fallido
      activeDeadlineSeconds: 1800  # 30 minutos
      # Política de reintentos
      backoffLimit: 2
      template:
        metadata:
          labels:
            app: rayuela
            component: db-maintenance
        spec:
          # Usar ServiceAccount con permisos mínimos
          serviceAccountName: db-maintenance-sa
          restartPolicy: OnFailure
          containers:
          - name: manage-partitions
            image: gcr.io/PROJECT_ID/rayuela-api:latest
            imagePullPolicy: IfNotPresent
            command:
            - python
            - -m
            - scripts.manage_partitions
            - --create
            - --archive
            - --inactivity-days
            - "365"
            - --buffer
            - "10"
            # Quitar el comentario de la siguiente línea para ejecutar cambios reales
            # - --execute
            resources:
              requests:
                cpu: 100m
                memory: 256Mi
              limits:
                cpu: 500m
                memory: 512Mi
            env:
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: username
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: password
            - name: POSTGRES_DB
              valueFrom:
                configMapKeyRef:
                  name: db-config
                  key: database
            - name: POSTGRES_SERVER
              valueFrom:
                configMapKeyRef:
                  name: db-config
                  key: host
            - name: POSTGRES_PORT
              valueFrom:
                configMapKeyRef:
                  name: db-config
                  key: port
            - name: PARTITION_SIZE
              value: "1000"
            - name: PARTITION_BUFFER_COUNT
              value: "10"
            - name: LOG_LEVEL
              value: "INFO"
---
# ServiceAccount con permisos mínimos para el CronJob
apiVersion: v1
kind: ServiceAccount
metadata:
  name: db-maintenance-sa
  namespace: rayuela
---
# Role con permisos mínimos necesarios
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: db-maintenance-role
  namespace: rayuela
rules:
- apiGroups: [""]
  resources: ["pods", "pods/log"]
  verbs: ["get", "list"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "watch"]
---
# RoleBinding para asociar el ServiceAccount con el Role
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: db-maintenance-rolebinding
  namespace: rayuela
subjects:
- kind: ServiceAccount
  name: db-maintenance-sa
  namespace: rayuela
roleRef:
  kind: Role
  name: db-maintenance-role
  apiGroup: rbac.authorization.k8s.io
