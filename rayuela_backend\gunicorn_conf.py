"""
Gunicorn configuration file for production deployment.
"""

import os
import multiprocessing

# Server socket
# Cloud Run sets PORT environment variable
# Use API_HOST for host binding (0.0.0.0 to listen on all interfaces)
bind = os.getenv("API_HOST", "0.0.0.0") + ":" + os.getenv("PORT", "8080")
backlog = 2048

# Worker processes
# Reducimos el número de workers para evitar problemas de memoria
cpu_count = multiprocessing.cpu_count()
# Cambiamos la fórmula a CPU cores + 1, con un máximo de 4 workers
default_workers = min(cpu_count + 1, 4)
workers = int(os.getenv("GUNICORN_WORKERS", default_workers))
worker_class = "uvicorn.workers.UvicornWorker"
# Optimizado para startup: reducir worker_connections para minimizar carga en BD
# Con 4 workers max y pool_size=8, esto da ~320 conexiones máximas vs 6000 anteriores
worker_connections = int(os.getenv("GUNICORN_WORKER_CONNECTIONS", "200"))

# Timeout ajustado para evitar workers bloqueados
# Aumentamos el timeout para permitir tiempo de carga de secretos en producción
timeout = int(os.getenv("GUNICORN_TIMEOUT", "120"))
# Añadimos un tiempo de gracia para finalizar trabajos en curso
graceful_timeout = 60

# Keep-alive para mejor reutilización de conexiones
keepalive = 5

# Process naming
proc_name = "rayuela-api"

# Server mechanics
daemon = False
pidfile = None
umask = 0
user = None
group = None
tmp_upload_dir = None

# Logging
errorlog = "-"
loglevel = os.getenv("LOG_LEVEL", "info")
accesslog = "-"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(L)s'

# Límites de recursos para prevenir fugas de memoria
max_requests = 200
max_requests_jitter = 50

# Server hooks
def on_starting(server):
    """Log when server starts."""
    server.log.info("Starting Rayuela")


def on_exit(server):
    """Log when server exits."""
    server.log.info("Shutting down Rayuela")
