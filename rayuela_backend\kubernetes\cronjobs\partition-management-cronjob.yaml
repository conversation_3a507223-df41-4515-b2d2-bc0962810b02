apiVersion: batch/v1
kind: CronJob
metadata:
  name: partition-management
  labels:
    app: rayuela
    component: maintenance
spec:
  schedule: "0 1 * * *"  # Ejecutar diariamente a la 1:00 AM
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 5
  jobTemplate:
    spec:
      backoffLimit: 2
      template:
        spec:
          containers:
          - name: partition-manager
            image: ${DOCKER_REGISTRY}/rayuela-backend:${IMAGE_TAG}
            command: ["python", "scripts/manage_partitions.py"]
            env:
              - name: POSTGRES_USER
                valueFrom:
                  secretKeyRef:
                    name: db-credentials
                    key: username
              - name: POSTGRES_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: db-credentials
                    key: password
              - name: POSTGRES_SERVER
                valueFrom:
                  configMapKeyRef:
                    name: app-config
                    key: db_host
              - name: POSTGRES_PORT
                valueFrom:
                  configMapKeyRef:
                    name: app-config
                    key: db_port
              - name: POSTGRES_DB
                valueFrom:
                  configMapKeyRef:
                    name: app-config
                    key: db_name
              - name: PARTITION_SIZE
                value: "100000"
              - name: PARTITION_BUFFER_COUNT
                value: "5"
              - name: PYTHONPATH
                value: "."
            resources:
              requests:
                cpu: 100m
                memory: 256Mi
              limits:
                cpu: 500m
                memory: 512Mi
          restartPolicy: OnFailure
